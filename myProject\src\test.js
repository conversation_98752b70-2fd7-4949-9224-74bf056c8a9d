#include "DBUtils";

(function () {
    var doc = getCurrentDocument();
    var sql = "SELECT * FROM tlk_GYPT_01_MAIN ";
    var flag = IsRole("系统管理员,总经理,供应链管理部负责人,综合部财务组,综合部数据组");
    var wherePart = "WHERE 1 = 1 ";
    var billNumber = doc.getItemValueAsString("编号");
    var pyBillNumber = doc.getItemValueAsString("普源采购订单");
    var cger = doc.getItemValueAsString("采购员");
    var purchaseBN = doc.getItemValueAsString("OA采购申请单");
    var orderBN = doc.getItemValueAsString("OA采购订单");
    var paymentBN = doc.getItemValueAsString("OA采购付款申请");
    var trackKind = doc.getItemValueAsString("跟踪类型");
    var sku = doc.getItemValueAsString("SKU");
    var stateLabel = doc.getItemValueAsString("审批状态");
    var delayDays = doc.getItemValueAsString("逾期天数");
    var isEnd = doc.getItemValueAsString("结束");
    if (isNotNull(billNumber))
        wherePart += " AND ITEM_编号 LIKE '%" + billNumber + "%' ";
    if (isNotNull(pyBillNumber))
        wherePart += " AND ITEM_普源采购订单编号 LIKE '%" + pyBillNumber + "%' ";
    if (isNotNull(cger))
        wherePart += " AND ITEM_采购员 LIKE '%" + cger + "%' ";
    if (isNotNull(purchaseBN))
        wherePart += " AND ITEM_采购申请单编号 LIKE '%" + purchaseBN + "%' ";
    if (isNotNull(orderBN))
        wherePart += " AND ITEM_采购订单编号 LIKE '%" + orderBN + "%' ";
    if (isNotNull(paymentBN))
        wherePart += " AND ITEM_采购付款单编号 LIKE '%" + paymentBN + "%' ";
    if (isNotNull(trackKind))
        wherePart += " AND ITEM_跟踪类型 LIKE '%" + trackKind + "%' ";
    if (isNotNull(sku))
        wherePart += " AND ITEM_SKU LIKE '%" + sku + "%' ";
    if (isNotNull(stateLabel))
        wherePart += " AND STATELABEL LIKE '%" + stateLabel + "%' ";
    if (isNotNull(delayDays))
        wherePart += " AND ITEM_本次逾期天数 >= " + delayDays + " ";
    if ("否".equals(isEnd))
        wherePart += " AND (STATELABEL <> '结束' AND STATELABEL <> '作废' AND AUDITORNAMES <> '') "
    sql += wherePart;
    debug("sql=" + sql);
    if (flag)
        return sql + " ORDER BY ITEM_跟踪类型, ITEM_跟踪类型 DESC ";
    else {
        var user = getWebUser();
        var flowId = "__AuEFr0wOVUvaNSk8YBI"; 
        var users = GetAgents(user, flowId);
        sql = UserSqlByUserField(sql, "采购员", users);
        return sql + " ORDER BY ITEM_跟踪类型, ITEM_编号 DESC ";
    }
})()


#include "DBUtils";

(function () {
  /* var sql = "SELECT * FROM tlk_gyfh_ft_01_freighttrial_by_month ";
  var flag = IsRole("系统管理员,总经理,供应链管理部负责人");
  if (flag == false){
    var flowId = "__eGfOEMOAWHCpZyRRC3v";
    var users = GetAgents(user, flowId);
    sql = UserSqlByUserField(sourceSql, "填写人", users);
  }
  sql += " ORDER BY ITEM_编号 DESC";
  return sql; */
  var sql = "SELECT * FROM tlk_gyfh_ft_01_freighttrial_by_month ";
  var flag = IsRole("系统管理员,总经理,供应链管理部负责人");
  if (flag == false){
    var user = getWebUser(); // 添加：在本函数内获取当前用户
    var flowId = "__eGfOEMOAWHCpZyRRC3v";
    var users = GetAgents(user, flowId);
    sql = UserSqlByUserField(sql, "填写人", users); // 使用 sql，避免未定义的 sourceSql
  }
  sql += " ORDER BY ITEM_编号 DESC";
  return sql;
})()


{
  "mcpServers": {
    "mysql": {
      "command": "npx",
      "args": [
        "-y",
        "@fhuang/mcp-mysql-server"
      ],
      "env": {
        "MYSQL_HOST": "************",
        "MYSQL_USER": "root",
        "MYSQL_PASSWORD": "Teemlink2010",
        "MYSQL_DATABASE": "magic5",
        "MYSQL_PORT": "3307"
      }
    }
  }
}

npx @fhuang/mcp-mysql-server mysql://root:Teemlink2010@************:3307/magic5

请使用mcp工具访问数据库，解释一下 tlk_psm_01_personal_affairs_management 表以下是MySQL的连接配置
"MYSQL_HOST": "************",
          "MYSQL_PORT": "3307",
          "MYSQL_USER": "root",
          "MYSQL_PASSWORD": "Teemlink2010",
          "MYSQL_DATABASE": "magic5"

{
  "mcpServers": {
    "mysql": {
      "command": "npx",
      "args": ["-y", "@fhuang/mcp-mysql-server"],
      "env": {
        "MYSQL_HOST": "************",
          "MYSQL_PORT": "3307",
          "MYSQL_USER": "root",
          "MYSQL_PASSWORD": "Teemlink2010",
          "MYSQL_DATABASE": "magic5"
      }
    }
  }
}

#include "SysUserUtils";

(function (){
  var user = GetAssistant();
  var applicationId = getApplication();//获取applicationId                            
  var docProcess = getDocProcess(applicationId);                            
  var orderMainDoc;                                
  //创建采购订单                            
  var cgdPrefix="FEAC-";                              
  var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                             
  var billFormName = "GWFE_01_Apply_Calculate";                            
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();                            
  var Form = formProcess.doViewByFormName(billFormName, applicationId);
  var params = createParamsTable();                            
  var doc = docProcess.doNew(Form, user, params);                            
  doc.addStringItem("编号",cgBillNo);                            
  doc.addStringItem("填写人", user.getName());
  doc.addDateItem("填写日期", getToday());
  docProcess.doCreate(doc);//创建                            
  //重新获取采购订单对象                            
  doc = LoadByIndexFieldValue("tlk_gwfe_01_apply_calculate", "ITEM_编号", cgBillNo, true);                              
  var flowid = "__klySHS2JvHgnWWaXel6";                      
  var billParams = createParamsTable();                                           
  billParams.setParameter("_flowid", flowid);                            
  docProcess.doStartFlowOrUpdate(doc, billParams, user); //启动流程 
})()
参考以上脚本，当我编辑保存tlk_psm_01_personal_affairs_management的内容的时候，能够在tlk_psm_01_personal_affairs_management_list表中自动创建事务提醒记录，请帮我实现功能

#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试
            debug("源数据字段列表:");
            var sourceKeys = sourceData.keySet().toArray();
            for (var k = 0; k < sourceKeys.length; k++) {
                debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            }

            // 获取系统对象
            var user = GetAssistant();
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 尝试多种可能的目标表单名称
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var possibleFormNames = [
                "PSM_01_Personal_Affairs_Management_List"
            ];

            var listForm = null;
            var actualFormName = "";

            for (var i = 0; i < possibleFormNames.length; i++) {
                try {
                    debug("尝试表单名称: " + possibleFormNames[i]);
                    listForm = formProcess.doViewByFormName(possibleFormNames[i], applicationId);
                    if (listForm) {
                        actualFormName = possibleFormNames[i];
                        debug("✓ 找到目标表单: " + actualFormName);
                        break;
                    }
                } catch (e) {
                    debug("✗ 表单名称失败: " + possibleFormNames[i] + " - " + e.message);
                }
            }

            if (listForm) {
                debug("确认找到目标表单: " + actualFormName);

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                try {
                    debug("调用 docProcess.doNew，参数: form=" + actualFormName + ", user=" + user.getName());
                    debug("listForm对象: " + listForm);
                    debug("user对象: " + user);
                    debug("params对象: " + params);

                    var listDoc = docProcess.doNew(listForm, user, params);
                    debug("✓ 新记录对象创建成功，对象: " + listDoc);

                    // 简单测试 - 只设置编号字段
                    debug("开始设置字段...");
                    listDoc.addStringItem("编号", billNo);
                    debug("✓ 设置编号成功: " + billNo);

                    // 创建记录
                    debug("开始执行 docProcess.doCreate...");
                    docProcess.doCreate(listDoc);
                    debug("✓ 成功创建提醒记录: " + billNo);

                } catch (newError) {
                    debug("✗ 创建过程失败: " + newError.message);
                    debug("错误详情: " + newError);
                    // 不抛出异常，继续执行
                }



            } else {
                debug("未找到任何匹配的目标表单，尝试的表单名称:");
                for (var j = 0; j < possibleFormNames.length; j++) {
                    debug("  - " + possibleFormNames[j]);
                }
            }
        } else {
            debug("未找到源数据，SQL: " + sql);
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("=== 创建提醒记录失败 ===");
    debug("错误消息: " + e.message);
    debug("错误详情: " + e);
    debug("========================");
} 

