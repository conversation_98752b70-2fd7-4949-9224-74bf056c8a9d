
#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    if (!currentDocId) return;

    var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
    var sourceData = MagicFindBySql("magic", sql);
    if (!sourceData) return;

    // 获取系统对象
    var applicationId = getApplication();
    var docProcess = getDocProcess(applicationId);
    var billNo = countNext2("PSMT-", true, true, true, 4);

    // 获取目标表单
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    var listForm = formProcess.doViewByFormName("PSM_01_Personal_Affairs_Management_List", applicationId);

    // 创建新记录
    var params = createParamsTable();
    var currentUser = getWebUser();

    try {
        // 使用OA API创建
        var listDoc = docProcess.doNew(listForm, currentUser, params);
        listDoc.addStringItem("编号", billNo);
        docProcess.doCreate(listDoc);
        debug("✓ 成功创建记录: " + billNo);
    } catch (e2) {
        // 备选方案：直接SQL插入
        var newId = "PSM" + new Date().getTime() + Math.floor(Math.random() * 1000);
        var insertSql = "INSERT INTO tlk_psm_01_personal_affairs_management_list " +
                      "(ID, ITEM_编号, ITEM_事务标题, ITEM_内容, ITEM_合规提醒, DOMAINID, APPLICATIONID, AUTHOR, CREATED) " +
                      "VALUES ('" + newId + "', '" + billNo + "', '" +
                      sourceData.get("ITEM_标题") + "', '" + sourceData.get("ITEM_内容") +
                      "', '是', '" + sourceData.get("domainid") + "', '" +
                      sourceData.get("applicationid") + "', '" + sourceData.get("author") +
                      "', NOW())";
        updateByDSName("magic", insertSql);
        debug("✓ SQL方式创建记录: " + billNo);
    }
} catch (e) {
    debug("创建提醒记录失败: " + e.message);
}
