#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试
            debug("源数据字段列表:");
            var sourceKeys = sourceData.keySet().toArray();
            for (var k = 0; k < sourceKeys.length; k++) {
                debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            }

            // 获取系统对象
            var user = GetAssistant();
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 尝试多种可能的目标表单名称
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var possibleFormNames = [
                "PSM_01_Personal_Affairs_Management_List"
            ];

            var listForm = null;
            var actualFormName = "";

            for (var i = 0; i < possibleFormNames.length; i++) {
                try {
                    debug("尝试表单名称: " + possibleFormNames[i]);
                    listForm = formProcess.doViewByFormName(possibleFormNames[i], applicationId);
                    if (listForm) {
                        actualFormName = possibleFormNames[i];
                        debug("✓ 找到目标表单: " + actualFormName);
                        break;
                    }
                } catch (e) {
                    debug("✗ 表单名称失败: " + possibleFormNames[i] + " - " + e.message);
                }
            }

            if (listForm) {
                debug("确认找到目标表单: " + actualFormName);

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                // 尝试不同的创建方法
                debug("方法1: 尝试使用 docProcess.doNew...");
                try {
                    var listDoc = docProcess.doNew(listForm, user, params);
                    debug("✓ 方法1成功: " + listDoc);

                    // 设置字段并保存
                    listDoc.addStringItem("编号", billNo);
                    debug("✓ 设置编号: " + billNo);

                    docProcess.doCreate(listDoc);
                    debug("✓ 成功创建记录: " + billNo);

                } catch (e1) {
                    debug("✗ 方法1失败: " + e1.message);

                    // 方法2: 尝试使用不同的用户
                    debug("方法2: 尝试使用当前用户...");
                    try {
                        var currentUser = getWebUser();
                        debug("当前用户: " + currentUser.getName());

                        var listDoc2 = docProcess.doNew(listForm, currentUser, params);
                        debug("✓ 方法2成功: " + listDoc2);

                        listDoc2.addStringItem("编号", billNo);
                        debug("✓ 设置编号: " + billNo);

                        docProcess.doCreate(listDoc2);
                        debug("✓ 成功创建记录: " + billNo);

                    } catch (e2) {
                        debug("✗ 方法2失败: " + e2.message);

                        // 方法3: 尝试直接SQL插入
                        debug("方法3: 尝试直接SQL插入...");
                        try {
                            var insertSql = "INSERT INTO tlk_psm_01_personal_affairs_management_list " +
                                          "(ID, ITEM_编号, ITEM_事务标题, ITEM_内容, ITEM_合规提醒, DOMAINID, APPLICATIONID, AUTHOR, CREATED) " +
                                          "VALUES ('" + generateUUID() + "', '" + billNo + "', '" +
                                          sourceData.get("ITEM_标题") + "', '" + sourceData.get("ITEM_内容") +
                                          "', '是', '" + sourceData.get("domainid") + "', '" +
                                          sourceData.get("applicationid") + "', '" + sourceData.get("author") +
                                          "', NOW())";
                            debug("SQL: " + insertSql);

                            var result = MagicExecuteBySql("magic", insertSql);
                            debug("✓ 方法3成功，SQL执行结果: " + result);

                        } catch (e3) {
                            debug("✗ 方法3失败: " + e3.message);
                        }
                    }
                }



            } else {
                debug("未找到任何匹配的目标表单，尝试的表单名称:");
                for (var j = 0; j < possibleFormNames.length; j++) {
                    debug("  - " + possibleFormNames[j]);
                }
            }
        } else {
            debug("未找到源数据，SQL: " + sql);
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("=== 创建提醒记录失败 ===");
    debug("错误消息: " + e.message);
    debug("错误详情: " + e);
    debug("========================");
} 