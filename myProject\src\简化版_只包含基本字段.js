#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试
            debug("源数据字段列表:");
            var sourceKeys = sourceData.keySet().toArray();
            for (var k = 0; k < sourceKeys.length; k++) {
                debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            }

            // 获取系统对象
            var user = GetAssistant();
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 直接使用确定的目标表单名称
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var listForm = formProcess.doViewByFormName("PSM_01_Personal_Affairs_Management_List", applicationId);

            if (listForm) {
                debug("找到目标表单: tlk_psm_01_personal_affairs_management_list");

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                var listDoc = docProcess.doNew(listForm, user, params);
                debug("新记录对象创建成功");

                // 设置字段
                listDoc.addStringItem("编号", billNo);
                debug("✓ 设置编号: " + billNo);

                // 获取事务标题
                var title = sourceData.get("ITEM_事务标题") ||
                           sourceData.get("ITEM_标题") ||
                           sourceData.get("ITEM_事务名称") ||
                           sourceData.get("ITEM_名称") || "个人事务";
                listDoc.addStringItem("事务标题", title);
                debug("✓ 设置事务标题: " + title);

                // 获取内容
                var content = sourceData.get("ITEM_事务描述") ||
                             sourceData.get("ITEM_备注") ||
                             sourceData.get("ITEM_内容") ||
                             sourceData.get("ITEM_说明") ||
                             sourceData.get("ITEM_描述") || "";
                listDoc.addStringItem("内容", content);
                debug("✓ 设置内容: " + content);

                // 设置合规提醒
                listDoc.addStringItem("合规提醒", "是");
                debug("✓ 设置合规提醒: 是");

                // 创建记录
                debug("开始执行 docProcess.doCreate...");
                docProcess.doCreate(listDoc);
                debug("✓ 成功创建提醒记录: " + billNo);

            } else {
                debug("未找到目标表单: tlk_psm_01_personal_affairs_management_list");
            }
        } else {
            debug("未找到源数据，SQL: " + sql);
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("=== 创建提醒记录失败 ===");
    debug("错误消息: " + e.message);
    debug("错误详情: " + e);
    debug("========================");
} 