
#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试（可选）
            // debug("源数据字段列表:");
            // var sourceKeys = sourceData.keySet().toArray();
            // for (var k = 0; k < sourceKeys.length; k++) {
            //     debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            // }

            // 获取系统对象
            // var user = GetAssistant(); // 不使用智能助手用户
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSML-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 获取目标表单（简化版，直接使用已知表单名）
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var formName = "PSM_01_Personal_Affairs_Management_List";

            try {
                debug("尝试表单名称: " + formName);
                var listForm = formProcess.doViewByFormName(formName, applicationId);
                debug("✓ 找到目标表单: " + formName);

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                // 方法2: 使用当前用户创建记录（主要方法）
                try {
                    var currentUser = getWebUser();
                    debug("当前用户: " + currentUser.getName());

                    var listDoc = docProcess.doNew(listForm, currentUser, params);
                    debug("✓ 方法2成功: " + listDoc);

                    // 设置基本字段
                    listDoc.addStringItem("编号", billNo);
                    debug("✓ 设置编号: " + billNo);

                    // 字段映射：原表 -> list表
                    // 填写人 -> 发起人
                    var originalUser = sourceData.get("ITEM_填写人");
                    if (originalUser) {
                        listDoc.addStringItem("发起人", originalUser);
                        debug("✓ 设置发起人: " + originalUser);
                    }

                    // 提醒对象 -> 办理人
                    var reminderTarget = sourceData.get("ITEM_提醒对象");
                    if (reminderTarget) {
                        try {
                            var userNames = [];
                            // 处理多个用户ID（分号分隔）
                            var userIds = reminderTarget.split(";");
                            for (var i = 0; i < userIds.length; i++) {
                                var userId = userIds[i].trim();
                                if (userId) {
                                    try {
                                        var user = getUserById(userId);
                                        if (user) {
                                            userNames.push(user.getName());
                                        } else {
                                            userNames.push(userId); // 如果用户不存在，使用ID
                                        }
                                    } catch (singleUserError) {
                                        userNames.push(userId); // 如果获取失败，使用ID
                                    }
                                }
                            }
                            var finalUserNames = userNames.join(";");
                            listDoc.addStringItem("办理人", finalUserNames);
                            debug("✓ 设置办理人: " + finalUserNames);
                        } catch (userError) {
                            debug("获取用户名失败，使用原始ID: " + userError.message);
                            listDoc.addStringItem("办理人", reminderTarget);
                            debug("✓ 设置办理人(ID): " + reminderTarget);
                        }
                    }

                    // 标题 -> 标题
                    var title = sourceData.get("ITEM_标题");
                    if (title) {
                        listDoc.addStringItem("标题", title);
                        debug("✓ 设置标题: " + title);
                    }

                    // 内容 -> 内容
                    var content = sourceData.get("ITEM_内容");
                    if (content) {
                        listDoc.addStringItem("内容", content);
                        debug("✓ 设置内容: " + content);
                    }

                    // 填写日期 -> 发起日期
                    var fillDate = sourceData.get("ITEM_填写日期");
                    if (fillDate) {
                        try {
                            listDoc.addDateItem("发起日期", fillDate);
                            debug("✓ 设置发起日期(日期): " + fillDate);
                        } catch (dateError) {
                            debug("日期设置失败，尝试字符串方式: " + dateError.message);
                            listDoc.addStringItem("发起日期", fillDate.toString());
                            debug("✓ 设置发起日期(字符串): " + fillDate);
                        }
                    }

                    debug("准备保存文档...");
                    try {
                        docProcess.doCreate(listDoc);
                        debug("✓ 成功创建记录: " + billNo);
                    } catch (createError) {
                        debug("✗ 文档创建失败: " + createError.message);
                        debug("错误详情: " + createError);
                        throw createError;
                    }

                } catch (e2) {
                    debug("✗ 方法2失败: " + e2.message);

                    // 方法3: 备选方案 - 直接SQL插入
                    debug("方法3: 尝试直接SQL插入...");
                    try {
                        var newId = "PSM" + new Date().getTime() + Math.floor(Math.random() * 1000);
                        var insertSql = "INSERT INTO tlk_psm_01_personal_affairs_management_list " +
                                      "(ID, ITEM_编号, ITEM_发起人, ITEM_办理人, ITEM_标题, ITEM_内容, ITEM_发起日期, ITEM_合规提醒, DOMAINID, APPLICATIONID, AUTHOR, CREATED) " +
                                      "VALUES ('" + newId + "', '" + billNo + "', '" +
                                      sourceData.get("ITEM_填写人") + "', '" + sourceData.get("ITEM_提醒对象") + "', '" +
                                      sourceData.get("ITEM_标题") + "', '" + sourceData.get("ITEM_内容") + "', '" +
                                      sourceData.get("ITEM_填写日期") + "', '是', '" + sourceData.get("domainid") + "', '" +
                                      sourceData.get("applicationid") + "', '" + sourceData.get("author") +
                                      "', NOW())";
                        debug("SQL: " + insertSql);

                        var result = updateByDSName("magic", insertSql);
                        debug("✓ 方法3成功，SQL执行结果: " + result);

                    } catch (e3) {
                        debug("✗ 方法3失败: " + e3.message);
                    }
                }

            } catch (e) {
                debug("✗ 表单获取失败: " + e.message);
            }
        } else {
            debug("未找到源数据");
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("创建提醒记录失败: " + e.message);
}
