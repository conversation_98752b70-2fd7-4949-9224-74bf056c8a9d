#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试
            debug("源数据字段列表:");
            var sourceKeys = sourceData.keySet().toArray();
            for (var k = 0; k < sourceKeys.length; k++) {
                debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            }

            // 获取系统对象
            var user = GetAssistant();
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 尝试多种可能的目标表单名称
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var possibleFormNames = [
                "PSM_01_Personal_Affairs_Management_List"
            ];

            var listForm = null;
            var actualFormName = "";

            for (var i = 0; i < possibleFormNames.length; i++) {
                try {
                    debug("尝试表单名称: " + possibleFormNames[i]);
                    listForm = formProcess.doViewByFormName(possibleFormNames[i], applicationId);
                    if (listForm) {
                        actualFormName = possibleFormNames[i];
                        debug("✓ 找到目标表单: " + actualFormName);
                        break;
                    }
                } catch (e) {
                    debug("✗ 表单名称失败: " + possibleFormNames[i] + " - " + e.message);
                }
            }

            if (listForm) {
                debug("确认找到目标表单: " + actualFormName);

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                try {
                    debug("调用 docProcess.doNew，参数: form=" + actualFormName + ", user=" + user.getName());
                    var listDoc = docProcess.doNew(listForm, user, params);
                    debug("✓ 新记录对象创建成功");
                } catch (newError) {
                    debug("✗ 创建新记录对象失败: " + newError.message);
                    debug("错误详情: " + newError);
                    throw newError;
                }

                // 设置字段
                listDoc.addStringItem("编号", billNo);
                debug("✓ 设置编号: " + billNo);

                // 获取事务标题
                var title = sourceData.get("ITEM_事务标题") ||
                           sourceData.get("ITEM_标题") ||
                           sourceData.get("ITEM_事务名称") ||
                           sourceData.get("ITEM_名称") || "个人事务";
                listDoc.addStringItem("事务标题", title);
                debug("✓ 设置事务标题: " + title);

                // 获取内容
                var content = sourceData.get("ITEM_事务描述") ||
                             sourceData.get("ITEM_备注") ||
                             sourceData.get("ITEM_内容") ||
                             sourceData.get("ITEM_说明") ||
                             sourceData.get("ITEM_描述") || "";
                listDoc.addStringItem("内容", content);
                debug("✓ 设置内容: " + content);

                // 设置合规提醒
                listDoc.addStringItem("合规提醒", "是");
                debug("✓ 设置合规提醒: 是");

                // 创建记录
                debug("开始执行 docProcess.doCreate...");
                docProcess.doCreate(listDoc);
                debug("✓ 成功创建提醒记录: " + billNo);

            } else {
                debug("未找到任何匹配的目标表单，尝试的表单名称:");
                for (var j = 0; j < possibleFormNames.length; j++) {
                    debug("  - " + possibleFormNames[j]);
                }
            }
        } else {
            debug("未找到源数据，SQL: " + sql);
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("=== 创建提醒记录失败 ===");
    debug("错误消息: " + e.message);
    debug("错误详情: " + e);
    debug("========================");
} 