
#include "SysUserUtils";
#include "DBUtils";

try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);

    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);

        if (sourceData) {
            debug("查询到源数据");

            // 先输出源数据的所有字段，便于调试（可选）
            // debug("源数据字段列表:");
            // var sourceKeys = sourceData.keySet().toArray();
            // for (var k = 0; k < sourceKeys.length; k++) {
            //     debug("  " + sourceKeys[k] + " = " + sourceData.get(sourceKeys[k]));
            // }

            // 获取系统对象
            // var user = GetAssistant(); // 不使用智能助手用户
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);

            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);

            // 获取目标表单（简化版，直接使用已知表单名）
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var formName = "PSM_01_Personal_Affairs_Management_List";

            try {
                debug("尝试表单名称: " + formName);
                var listForm = formProcess.doViewByFormName(formName, applicationId);
                debug("✓ 找到目标表单: " + formName);

                // 创建新记录
                debug("开始创建新记录...");
                var params = createParamsTable();
                debug("参数表创建成功");

                // 方法2: 使用当前用户创建记录（主要方法）
                try {
                    var currentUser = getWebUser();
                    debug("当前用户: " + currentUser.getName());

                    var listDoc = docProcess.doNew(listForm, currentUser, params);
                    debug("✓ 方法2成功: " + listDoc);

                    listDoc.addStringItem("编号", billNo);
                    debug("✓ 设置编号: " + billNo);

                    docProcess.doCreate(listDoc);
                    debug("✓ 成功创建记录: " + billNo);

                } catch (e2) {
                    /* debug("✗ 方法2失败: " + e2.message);

                    // 方法3: 备选方案 - 直接SQL插入
                    debug("方法3: 尝试直接SQL插入...");
                    try {
                        var newId = "PSM" + new Date().getTime() + Math.floor(Math.random() * 1000);
                        var insertSql = "INSERT INTO tlk_psm_01_personal_affairs_management_list " +
                                      "(ID, ITEM_编号, ITEM_事务标题, ITEM_内容, ITEM_合规提醒, DOMAINID, APPLICATIONID, AUTHOR, CREATED) " +
                                      "VALUES ('" + newId + "', '" + billNo + "', '" +
                                      sourceData.get("ITEM_标题") + "', '" + sourceData.get("ITEM_内容") +
                                      "', '是', '" + sourceData.get("domainid") + "', '" +
                                      sourceData.get("applicationid") + "', '" + sourceData.get("author") +
                                      "', NOW())";
                        debug("SQL: " + insertSql);

                        var result = updateByDSName("magic", insertSql);
                        debug("✓ 方法3成功，SQL执行结果: " + result);

                    } catch (e3) {
                        debug("✗ 方法3失败: " + e3.message);
                    } */
                }

            } catch (e) {
                debug("✗ 表单获取失败: " + e.message);
            }
        } else {
            debug("未找到源数据");
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("创建提醒记录失败: " + e.message);
}
